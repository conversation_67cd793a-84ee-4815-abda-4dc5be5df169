#!/usr/bin/env python3
"""
使用数据预处理功能运行SoftPatch的示例脚本
"""

import subprocess
import sys
import os


def run_with_geometric_augmentation():
    """运行带有几何变换增强的训练"""
    
    cmd = [
        sys.executable, "main.py",
        "--dataset", "mvtec_enhanced",  # 使用增强版数据集
        "--data_path", "path/to/your/mvtec/data",  # 请修改为您的数据路径
        "--subdatasets", "bottle",
        "--backbone_names", "wideresnet50",
        "--layers_to_extract_from", "layer2", "layer3",
        "--batch_size", "8",
        
        # 启用几何变换增强
        "--enable_geometric_aug",
        "--rotation_range", "-15", "15",  # 旋转范围：-15到15度
        "--translation_range", "-0.15", "0.15",  # 平移范围：-15%到15%
        "--scale_range", "0.85", "1.15",  # 缩放范围：85%到115%
        "--aug_prob", "0.6",  # 60%概率应用增强
        
        # 启用图像对齐
        "--enable_image_alignment",
        "--alignment_method", "feature_based",
        "--reference_mode", "adaptive",
        
        "--results_path", "results_with_geometric_aug"
    ]
    
    print("运行带有几何变换增强的训练...")
    print(" ".join(cmd))
    
    try:
        subprocess.run(cmd, check=True)
        print("几何变换增强训练完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"训练失败，错误码: {e.returncode}")
        return False


def run_with_camera_shake():
    """运行带有相机抖动模拟的训练"""
    
    cmd = [
        sys.executable, "main.py",
        "--dataset", "mvtec_enhanced",
        "--data_path", "path/to/your/mvtec/data",
        "--subdatasets", "bottle",
        "--backbone_names", "wideresnet50",
        "--layers_to_extract_from", "layer2", "layer3",
        "--batch_size", "8",
        
        # 启用相机抖动模拟
        "--enable_camera_shake",
        "--aug_prob", "0.4",  # 40%概率应用抖动
        
        # 启用图像对齐
        "--enable_image_alignment",
        "--alignment_method", "phase_correlation",  # 相位相关对齐适合处理抖动
        "--reference_mode", "template",
        
        "--results_path", "results_with_camera_shake"
    ]
    
    print("运行带有相机抖动模拟的训练...")
    print(" ".join(cmd))
    
    try:
        subprocess.run(cmd, check=True)
        print("相机抖动模拟训练完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"训练失败，错误码: {e.returncode}")
        return False


def run_with_tta():
    """运行带有测试时增强的推理"""
    
    cmd = [
        sys.executable, "main.py",
        "--dataset", "mvtec_enhanced",
        "--data_path", "path/to/your/mvtec/data",
        "--subdatasets", "bottle",
        "--backbone_names", "wideresnet50",
        "--layers_to_extract_from", "layer2", "layer3",
        "--batch_size", "4",  # TTA时减小batch size
        
        # 启用测试时增强
        "--enable_tta",
        "--tta_num_aug", "7",  # 7种不同的增强
        
        # 启用图像对齐
        "--enable_image_alignment",
        "--alignment_method", "feature_based",
        "--reference_mode", "adaptive",
        
        "--results_path", "results_with_tta"
    ]
    
    print("运行带有测试时增强的推理...")
    print(" ".join(cmd))
    
    try:
        subprocess.run(cmd, check=True)
        print("TTA推理完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"推理失败，错误码: {e.returncode}")
        return False


def run_comprehensive_setup():
    """运行综合设置（所有功能启用）"""
    
    cmd = [
        sys.executable, "main.py",
        "--dataset", "mvtec_enhanced",
        "--data_path", "path/to/your/mvtec/data",
        "--subdatasets", "bottle",
        "--backbone_names", "wideresnet50",
        "--layers_to_extract_from", "layer2", "layer3",
        "--batch_size", "6",
        
        # 训练时数据增强
        "--enable_geometric_aug",
        "--enable_camera_shake",
        "--rotation_range", "-10", "10",
        "--translation_range", "-0.1", "0.1",
        "--scale_range", "0.9", "1.1",
        "--aug_prob", "0.5",
        
        # 测试时增强
        "--enable_tta",
        "--tta_num_aug", "5",
        
        # 图像对齐
        "--enable_image_alignment",
        "--alignment_method", "feature_based",
        "--reference_mode", "adaptive",
        
        "--results_path", "results_comprehensive"
    ]
    
    print("运行综合设置（所有功能启用）...")
    print(" ".join(cmd))
    
    try:
        subprocess.run(cmd, check=True)
        print("综合设置运行完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"运行失败，错误码: {e.returncode}")
        return False


def run_comparison_study():
    """运行对比研究"""
    
    configs = [
        {
            "name": "baseline",
            "args": [
                "--dataset", "mvtec",  # 原始数据集
                "--results_path", "results_baseline"
            ]
        },
        {
            "name": "with_alignment_only",
            "args": [
                "--dataset", "mvtec",
                "--enable_image_alignment",
                "--alignment_method", "feature_based",
                "--reference_mode", "template",
                "--results_path", "results_alignment_only"
            ]
        },
        {
            "name": "with_preprocessing_only",
            "args": [
                "--dataset", "mvtec_enhanced",
                "--enable_geometric_aug",
                "--enable_camera_shake",
                "--aug_prob", "0.5",
                "--results_path", "results_preprocessing_only"
            ]
        },
        {
            "name": "with_both",
            "args": [
                "--dataset", "mvtec_enhanced",
                "--enable_geometric_aug",
                "--enable_camera_shake",
                "--aug_prob", "0.5",
                "--enable_image_alignment",
                "--alignment_method", "feature_based",
                "--reference_mode", "adaptive",
                "--results_path", "results_both"
            ]
        },
        {
            "name": "with_all_features",
            "args": [
                "--dataset", "mvtec_enhanced",
                "--enable_geometric_aug",
                "--enable_camera_shake",
                "--aug_prob", "0.5",
                "--enable_tta",
                "--tta_num_aug", "5",
                "--enable_image_alignment",
                "--alignment_method", "feature_based",
                "--reference_mode", "adaptive",
                "--results_path", "results_all_features"
            ]
        }
    ]
    
    base_cmd = [
        sys.executable, "main.py",
        "--data_path", "path/to/your/mvtec/data",
        "--subdatasets", "bottle",
        "--backbone_names", "wideresnet50",
        "--layers_to_extract_from", "layer2", "layer3",
        "--batch_size", "8"
    ]
    
    print("开始对比研究...")
    results = {}
    
    for config in configs:
        print(f"\n运行配置: {config['name']}")
        cmd = base_cmd + config['args']
        print(" ".join(cmd))
        
        try:
            subprocess.run(cmd, check=True)
            results[config['name']] = "成功"
            print(f"配置 {config['name']} 完成")
        except subprocess.CalledProcessError as e:
            results[config['name']] = f"失败 (错误码: {e.returncode})"
            print(f"配置 {config['name']} 失败")
    
    print("\n=== 对比研究结果 ===")
    for name, result in results.items():
        print(f"{name:20s}: {result}")
    
    print("\n请查看以下目录中的结果:")
    for config in configs:
        result_path = [arg for i, arg in enumerate(config['args']) if config['args'][i-1] == '--results_path'][0]
        print(f"- {result_path}/")


def show_usage_examples():
    """显示使用示例"""
    
    print("数据预处理功能使用示例:")
    print("="*50)
    
    examples = [
        {
            "name": "基础几何变换增强",
            "description": "训练时应用旋转、平移、缩放变换",
            "cmd": [
                "python main.py",
                "--dataset mvtec_enhanced",
                "--data_path /path/to/data",
                "--subdatasets bottle",
                "--enable_geometric_aug",
                "--rotation_range -10 10",
                "--translation_range -0.1 0.1",
                "--scale_range 0.9 1.1",
                "--aug_prob 0.5"
            ]
        },
        {
            "name": "相机抖动模拟",
            "description": "模拟相机抖动和轻微模糊",
            "cmd": [
                "python main.py",
                "--dataset mvtec_enhanced",
                "--data_path /path/to/data",
                "--subdatasets bottle",
                "--enable_camera_shake",
                "--aug_prob 0.3"
            ]
        },
        {
            "name": "测试时增强 (TTA)",
            "description": "推理时使用多种变换并聚合结果",
            "cmd": [
                "python main.py",
                "--dataset mvtec_enhanced",
                "--data_path /path/to/data",
                "--subdatasets bottle",
                "--enable_tta",
                "--tta_num_aug 7"
            ]
        },
        {
            "name": "综合方案",
            "description": "结合所有预处理和对齐功能",
            "cmd": [
                "python main.py",
                "--dataset mvtec_enhanced",
                "--data_path /path/to/data",
                "--subdatasets bottle",
                "--enable_geometric_aug",
                "--enable_camera_shake",
                "--enable_tta",
                "--enable_image_alignment",
                "--alignment_method feature_based",
                "--reference_mode adaptive"
            ]
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['name']}:")
        print(f"   描述: {example['description']}")
        print("   命令: " + " \\\n         ".join(example['cmd']))
    
    print("\n" + "="*50)
    print("参数说明:")
    print("--enable_geometric_aug       启用几何变换增强")
    print("--enable_camera_shake        启用相机抖动模拟")
    print("--enable_tta                 启用测试时增强")
    print("--rotation_range             旋转角度范围（度）")
    print("--translation_range          平移范围（图像尺寸比例）")
    print("--scale_range                缩放范围")
    print("--aug_prob                   应用增强的概率")
    print("--tta_num_aug                TTA增强数量")


def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "geometric":
            run_with_geometric_augmentation()
        elif sys.argv[1] == "shake":
            run_with_camera_shake()
        elif sys.argv[1] == "tta":
            run_with_tta()
        elif sys.argv[1] == "comprehensive":
            run_comprehensive_setup()
        elif sys.argv[1] == "compare":
            run_comparison_study()
        elif sys.argv[1] == "examples":
            show_usage_examples()
        else:
            print("未知参数，请使用: geometric, shake, tta, comprehensive, compare, 或 examples")
    else:
        print("数据预处理功能使用脚本")
        print("="*30)
        print("用法:")
        print("  python run_with_preprocessing.py geometric      # 几何变换增强")
        print("  python run_with_preprocessing.py shake          # 相机抖动模拟")
        print("  python run_with_preprocessing.py tta            # 测试时增强")
        print("  python run_with_preprocessing.py comprehensive  # 综合设置")
        print("  python run_with_preprocessing.py compare        # 对比研究")
        print("  python run_with_preprocessing.py examples       # 显示使用示例")
        print("\n注意: 请先修改脚本中的数据路径!")


if __name__ == "__main__":
    main()
