#!/usr/bin/env python3
"""
使用现有数据增强功能的示例脚本
基于模型已有的noise_augmentation功能
"""

import subprocess
import sys
import os


def run_with_existing_rotation():
    """使用现有的旋转功能"""
    
    cmd = [
        sys.executable, "main.py",
        "--dataset", "mvtec",
        "--data_path", "path/to/your/mvtec/data",  # 请修改为您的数据路径
        "--subdatasets", "bottle",
        "--backbone_names", "wideresnet50",
        "--layers_to_extract_from", "layer2", "layer3",
        "--batch_size", "8",
        
        # 启用现有的噪声增强功能
        "--noise_augmentation",  # 这是关键参数
        "--noise", "0.1",  # 添加10%的异常样本进行增强
        
        # 控制增强的具体参数
        "--aug_enable_rotation",  # 启用旋转
        "--aug_rotation_degrees", "15",  # 旋转角度±15度
        "--aug_translation", "0.15", "0.15",  # 平移范围±15%
        "--aug_scale", "0.85", "1.15",  # 缩放范围85%-115%
        "--aug_shear", "10",  # 剪切角度±10度
        
        # 同时启用图像对齐来处理变换
        "--enable_image_alignment",
        "--alignment_method", "feature_based",
        "--reference_mode", "adaptive",
        
        "--results_path", "results_existing_aug"
    ]
    
    print("使用现有的数据增强功能...")
    print("命令:")
    print(" ".join(cmd))
    print("\n" + "="*50)
    
    try:
        subprocess.run(cmd, check=True)
        print("\n" + "="*50)
        print("运行成功完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n运行失败，错误码: {e.returncode}")
        return False


def run_rotation_only():
    """仅启用旋转变换"""
    
    cmd = [
        sys.executable, "main.py",
        "--dataset", "mvtec",
        "--data_path", "path/to/your/mvtec/data",
        "--subdatasets", "bottle",
        "--backbone_names", "wideresnet50",
        "--layers_to_extract_from", "layer2", "layer3",
        "--batch_size", "8",
        
        # 启用噪声增强，但只使用旋转
        "--noise_augmentation",
        "--noise", "0.05",
        "--aug_enable_rotation",  # 只启用旋转
        "--aug_rotation_degrees", "20",  # 更大的旋转角度
        "--aug_enable_affine",  # 禁用仿射变换
        "--aug_enable_noise",  # 禁用噪声添加
        
        # 启用图像对齐
        "--enable_image_alignment",
        "--alignment_method", "feature_based",
        "--reference_mode", "template",
        
        "--results_path", "results_rotation_only"
    ]
    
    print("仅启用旋转变换...")
    print("命令:")
    print(" ".join(cmd))
    
    try:
        subprocess.run(cmd, check=True)
        print("旋转变换运行完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"运行失败，错误码: {e.returncode}")
        return False


def run_heavy_augmentation():
    """重度数据增强"""
    
    cmd = [
        sys.executable, "main.py",
        "--dataset", "mvtec",
        "--data_path", "path/to/your/mvtec/data",
        "--subdatasets", "bottle",
        "--backbone_names", "wideresnet50",
        "--layers_to_extract_from", "layer2", "layer3",
        "--batch_size", "6",  # 减小batch size
        
        # 重度增强设置
        "--noise_augmentation",
        "--noise", "0.2",  # 增加到20%
        "--aug_enable_rotation",
        "--aug_enable_affine", 
        "--aug_enable_noise",
        "--aug_rotation_degrees", "25",  # 更大的旋转角度
        "--aug_translation", "0.2", "0.2",  # 更大的平移
        "--aug_scale", "0.8", "1.2",  # 更大的缩放范围
        "--aug_shear", "15",  # 更大的剪切
        
        # 强力图像对齐
        "--enable_image_alignment",
        "--alignment_method", "feature_based",
        "--reference_mode", "adaptive",
        
        "--results_path", "results_heavy_aug"
    ]
    
    print("重度数据增强...")
    print("命令:")
    print(" ".join(cmd))
    
    try:
        subprocess.run(cmd, check=True)
        print("重度增强运行完成！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"运行失败，错误码: {e.returncode}")
        return False


def run_comparison():
    """对比不同设置的效果"""
    
    configs = [
        {
            "name": "无增强",
            "args": ["--results_path", "results_no_aug"]
        },
        {
            "name": "轻度增强",
            "args": [
                "--noise_augmentation", "--noise", "0.05",
                "--aug_rotation_degrees", "5",
                "--aug_translation", "0.05", "0.05",
                "--results_path", "results_light_aug"
            ]
        },
        {
            "name": "中度增强",
            "args": [
                "--noise_augmentation", "--noise", "0.1",
                "--aug_rotation_degrees", "10",
                "--aug_translation", "0.1", "0.1",
                "--results_path", "results_medium_aug"
            ]
        },
        {
            "name": "重度增强",
            "args": [
                "--noise_augmentation", "--noise", "0.15",
                "--aug_rotation_degrees", "20",
                "--aug_translation", "0.15", "0.15",
                "--aug_scale", "0.85", "1.15",
                "--results_path", "results_heavy_aug_comp"
            ]
        },
        {
            "name": "增强+对齐",
            "args": [
                "--noise_augmentation", "--noise", "0.1",
                "--aug_rotation_degrees", "15",
                "--aug_translation", "0.1", "0.1",
                "--enable_image_alignment",
                "--alignment_method", "feature_based",
                "--reference_mode", "adaptive",
                "--results_path", "results_aug_plus_alignment"
            ]
        }
    ]
    
    base_cmd = [
        sys.executable, "main.py",
        "--dataset", "mvtec",
        "--data_path", "path/to/your/mvtec/data",
        "--subdatasets", "bottle",
        "--backbone_names", "wideresnet50",
        "--layers_to_extract_from", "layer2", "layer3",
        "--batch_size", "8"
    ]
    
    print("开始对比不同增强设置...")
    results = {}
    
    for config in configs:
        print(f"\n运行配置: {config['name']}")
        cmd = base_cmd + config['args']
        print(" ".join(cmd))
        
        try:
            subprocess.run(cmd, check=True)
            results[config['name']] = "成功"
            print(f"配置 {config['name']} 完成")
        except subprocess.CalledProcessError as e:
            results[config['name']] = f"失败 (错误码: {e.returncode})"
            print(f"配置 {config['name']} 失败")
    
    print("\n=== 对比结果 ===")
    for name, result in results.items():
        print(f"{name:15s}: {result}")


def show_existing_features():
    """显示现有功能的使用方法"""
    
    print("SoftPatch现有数据增强功能说明:")
    print("="*50)
    
    print("\n1. 基础启用方法:")
    print("   --noise_augmentation              # 启用数据增强")
    print("   --noise 0.1                      # 添加10%异常样本进行增强")
    
    print("\n2. 详细控制参数:")
    print("   --aug_enable_rotation             # 启用旋转变换")
    print("   --aug_enable_affine               # 启用仿射变换")
    print("   --aug_enable_noise                # 启用噪声添加")
    print("   --aug_rotation_degrees 15         # 旋转角度±15度")
    print("   --aug_translation 0.1 0.1         # 平移范围±10%")
    print("   --aug_scale 0.9 1.1               # 缩放范围90%-110%")
    print("   --aug_shear 10                    # 剪切角度±10度")
    
    print("\n3. 结合图像对齐:")
    print("   --enable_image_alignment          # 启用图像对齐")
    print("   --alignment_method feature_based  # 对齐方法")
    print("   --reference_mode adaptive         # 参考模式")
    
    print("\n4. 完整示例:")
    example_cmd = [
        "python main.py",
        "--dataset mvtec",
        "--data_path /path/to/data",
        "--subdatasets bottle",
        "--noise_augmentation",
        "--noise 0.1",
        "--aug_rotation_degrees 15",
        "--aug_translation 0.1 0.1",
        "--enable_image_alignment",
        "--alignment_method feature_based"
    ]
    print("   " + " \\\n   ".join(example_cmd))
    
    print("\n" + "="*50)
    print("注意事项:")
    print("- noise_augmentation 必须启用才能使用数据增强")
    print("- noise 参数控制增强数据的比例")
    print("- 建议同时启用图像对齐来处理几何变换")
    print("- 可以选择性启用/禁用不同类型的变换")


def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "existing":
            run_with_existing_rotation()
        elif sys.argv[1] == "rotation":
            run_rotation_only()
        elif sys.argv[1] == "heavy":
            run_heavy_augmentation()
        elif sys.argv[1] == "compare":
            run_comparison()
        elif sys.argv[1] == "features":
            show_existing_features()
        else:
            print("未知参数，请使用: existing, rotation, heavy, compare, 或 features")
    else:
        print("现有数据增强功能使用脚本")
        print("="*30)
        print("用法:")
        print("  python run_existing_augmentation.py existing   # 使用现有增强功能")
        print("  python run_existing_augmentation.py rotation   # 仅旋转变换")
        print("  python run_existing_augmentation.py heavy      # 重度增强")
        print("  python run_existing_augmentation.py compare    # 对比不同设置")
        print("  python run_existing_augmentation.py features   # 显示功能说明")
        print("\n注意: 请先修改脚本中的数据路径!")


if __name__ == "__main__":
    main()
