"""
Image Alignment Module for SoftPatch
解决图像抖动、旋转等变换导致的patch位置偏移问题
"""

import torch
import torch.nn.functional as F
import cv2
import numpy as np
import logging
from typing import Optional, Tuple, List
from sklearn.cluster import KMeans

LOGGER = logging.getLogger(__name__)


class ImageAlignmentModule:
    """
    图像对齐模块，用于解决图像变换导致的patch位置偏移问题
    支持多种对齐策略，不改变原有模型框架
    """
    
    def __init__(self, 
                 alignment_method: str = "feature_based",
                 reference_mode: str = "template",
                 enable_alignment: bool = True,
                 max_features: int = 1000,
                 match_threshold: float = 0.7):
        """
        初始化图像对齐模块
        
        Args:
            alignment_method: 对齐方法 ("feature_based", "phase_correlation", "template_matching")
            reference_mode: 参考模式 ("template", "centroid", "adaptive")
            enable_alignment: 是否启用对齐功能
            max_features: 特征点最大数量
            match_threshold: 特征匹配阈值
        """
        self.alignment_method = alignment_method
        self.reference_mode = reference_mode
        self.enable_alignment = enable_alignment
        self.max_features = max_features
        self.match_threshold = match_threshold
        
        # 存储参考模板
        self.reference_template = None
        self.reference_features = None
        self.reference_keypoints = None
        
        # 初始化特征检测器
        self._init_feature_detector()
        
        LOGGER.info(f"ImageAlignmentModule initialized with method: {alignment_method}")
    
    def _init_feature_detector(self):
        """初始化特征检测器"""
        try:
            # 使用ORB特征检测器（更稳定，不受专利限制）
            self.feature_detector = cv2.ORB_create(nfeatures=self.max_features)
            self.matcher = cv2.BFMatcher(cv2.NORM_HAMMING, crossCheck=True)
        except Exception as e:
            LOGGER.warning(f"Failed to initialize ORB detector: {e}, falling back to simple alignment")
            self.feature_detector = None
            self.matcher = None
    
    def set_reference_template(self, images: torch.Tensor):
        """
        设置参考模板（在训练阶段调用）
        
        Args:
            images: 训练图像张量 [B, C, H, W]
        """
        if not self.enable_alignment:
            return
            
        # 转换为numpy格式
        if isinstance(images, torch.Tensor):
            images_np = self._tensor_to_numpy(images)
        else:
            images_np = images
            
        if self.reference_mode == "template":
            # 使用第一张图像作为模板
            self.reference_template = images_np[0]
        elif self.reference_mode == "centroid":
            # 使用所有图像的质心作为模板
            self.reference_template = np.mean(images_np, axis=0)
        elif self.reference_mode == "adaptive":
            # 使用聚类选择最具代表性的图像
            self._create_adaptive_template(images_np)
        
        # 提取参考特征
        if self.feature_detector is not None:
            self._extract_reference_features()
            
        LOGGER.info(f"Reference template set with mode: {self.reference_mode}")
    
    def _create_adaptive_template(self, images_np: np.ndarray):
        """创建自适应模板"""
        try:
            # 计算图像特征用于聚类
            features = []
            for img in images_np:
                # 使用简单的统计特征
                gray = cv2.cvtColor((img * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
                hist = cv2.calcHist([gray], [0], None, [256], [0, 256]).flatten()
                features.append(hist)
            
            features = np.array(features)
            
            # 使用K-means聚类
            n_clusters = min(5, len(images_np))
            kmeans = KMeans(n_clusters=n_clusters, random_state=42)
            labels = kmeans.fit_predict(features)
            
            # 选择最大簇的质心对应的图像
            largest_cluster = np.bincount(labels).argmax()
            cluster_indices = np.where(labels == largest_cluster)[0]
            self.reference_template = np.mean(images_np[cluster_indices], axis=0)
            
        except Exception as e:
            LOGGER.warning(f"Adaptive template creation failed: {e}, using first image")
            self.reference_template = images_np[0]
    
    def _extract_reference_features(self):
        """提取参考图像的特征点"""
        if self.feature_detector is None or self.reference_template is None:
            return
            
        try:
            # 转换为灰度图
            gray_ref = cv2.cvtColor((self.reference_template * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
            
            # 检测特征点
            self.reference_keypoints, self.reference_features = self.feature_detector.detectAndCompute(gray_ref, None)
            
        except Exception as e:
            LOGGER.warning(f"Feature extraction failed: {e}")
            self.reference_keypoints = None
            self.reference_features = None
    
    def align_images(self, images: torch.Tensor) -> torch.Tensor:
        """
        对齐图像（在推理阶段调用）
        
        Args:
            images: 输入图像张量 [B, C, H, W]
            
        Returns:
            对齐后的图像张量
        """
        if not self.enable_alignment or self.reference_template is None:
            return images
            
        aligned_images = []
        
        for i in range(images.shape[0]):
            img = images[i]
            aligned_img = self._align_single_image(img)
            aligned_images.append(aligned_img)
        
        return torch.stack(aligned_images, dim=0)
    
    def _align_single_image(self, image: torch.Tensor) -> torch.Tensor:
        """对齐单张图像"""
        try:
            # 转换为numpy
            img_np = self._tensor_to_numpy(image.unsqueeze(0))[0]
            
            if self.alignment_method == "feature_based":
                aligned_img = self._feature_based_alignment(img_np)
            elif self.alignment_method == "phase_correlation":
                aligned_img = self._phase_correlation_alignment(img_np)
            elif self.alignment_method == "template_matching":
                aligned_img = self._template_matching_alignment(img_np)
            else:
                aligned_img = img_np
                
            # 转换回tensor
            return self._numpy_to_tensor(aligned_img)
            
        except Exception as e:
            LOGGER.warning(f"Image alignment failed: {e}, returning original image")
            return image
    
    def _feature_based_alignment(self, image: np.ndarray) -> np.ndarray:
        """基于特征点的对齐"""
        if self.feature_detector is None or self.reference_features is None:
            return image
            
        try:
            # 转换为灰度图
            gray_img = cv2.cvtColor((image * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
            
            # 检测特征点
            keypoints, features = self.feature_detector.detectAndCompute(gray_img, None)
            
            if features is None or len(keypoints) < 4:
                return image
                
            # 特征匹配
            matches = self.matcher.match(self.reference_features, features)
            matches = sorted(matches, key=lambda x: x.distance)
            
            # 过滤好的匹配
            good_matches = [m for m in matches if m.distance < self.match_threshold * matches[0].distance]
            
            if len(good_matches) < 4:
                return image
                
            # 提取匹配点
            src_pts = np.float32([self.reference_keypoints[m.queryIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            dst_pts = np.float32([keypoints[m.trainIdx].pt for m in good_matches]).reshape(-1, 1, 2)
            
            # 计算变换矩阵
            M, mask = cv2.findHomography(dst_pts, src_pts, cv2.RANSAC, 5.0)
            
            if M is None:
                return image
                
            # 应用变换
            h, w = image.shape[:2]
            aligned_img = cv2.warpPerspective((image * 255).astype(np.uint8), M, (w, h))
            
            return aligned_img.astype(np.float32) / 255.0
            
        except Exception as e:
            LOGGER.warning(f"Feature-based alignment failed: {e}")
            return image
    
    def _phase_correlation_alignment(self, image: np.ndarray) -> np.ndarray:
        """基于相位相关的对齐"""
        try:
            # 转换为灰度图
            gray_ref = cv2.cvtColor((self.reference_template * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
            gray_img = cv2.cvtColor((image * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
            
            # 计算相位相关
            shift = cv2.phaseCorrelate(gray_ref.astype(np.float32), gray_img.astype(np.float32))
            
            # 应用平移变换
            M = np.float32([[1, 0, shift[0][0]], [0, 1, shift[0][1]]])
            h, w = image.shape[:2]
            aligned_img = cv2.warpAffine((image * 255).astype(np.uint8), M, (w, h))
            
            return aligned_img.astype(np.float32) / 255.0
            
        except Exception as e:
            LOGGER.warning(f"Phase correlation alignment failed: {e}")
            return image
    
    def _template_matching_alignment(self, image: np.ndarray) -> np.ndarray:
        """基于模板匹配的对齐"""
        try:
            # 转换为灰度图
            gray_ref = cv2.cvtColor((self.reference_template * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
            gray_img = cv2.cvtColor((image * 255).astype(np.uint8), cv2.COLOR_RGB2GRAY)
            
            # 模板匹配
            result = cv2.matchTemplate(gray_img, gray_ref, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)
            
            if max_val < 0.5:  # 匹配度太低
                return image
                
            # 计算偏移
            h, w = gray_ref.shape
            shift_x = max_loc[0] - w // 2
            shift_y = max_loc[1] - h // 2
            
            # 应用平移变换
            M = np.float32([[1, 0, -shift_x], [0, 1, -shift_y]])
            aligned_img = cv2.warpAffine((image * 255).astype(np.uint8), M, (w, h))
            
            return aligned_img.astype(np.float32) / 255.0
            
        except Exception as e:
            LOGGER.warning(f"Template matching alignment failed: {e}")
            return image
    
    def _tensor_to_numpy(self, tensor: torch.Tensor) -> np.ndarray:
        """将tensor转换为numpy数组"""
        if tensor.is_cuda:
            tensor = tensor.cpu()
        # [B, C, H, W] -> [B, H, W, C]
        return tensor.permute(0, 2, 3, 1).numpy()
    
    def _numpy_to_tensor(self, array: np.ndarray) -> torch.Tensor:
        """将numpy数组转换为tensor"""
        # [H, W, C] -> [C, H, W]
        if len(array.shape) == 3:
            tensor = torch.from_numpy(array).permute(2, 0, 1)
        else:
            tensor = torch.from_numpy(array)
        return tensor.float()
