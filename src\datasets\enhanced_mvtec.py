"""
增强版MVTec数据集，支持旋转和抖动预处理
"""

import os
import random
from enum import Enum
import PIL
import torch
from torchvision import transforms
import numpy as np
import cv2
from .mvtec import MVTecDataset, DatasetSplit, IMAGENET_MEAN, IMAGENET_STD, _CLASSNAMES


class GeometricAugmentation:
    """几何变换增强类"""
    
    def __init__(self, 
                 enable_rotation=True,
                 enable_translation=True, 
                 enable_scaling=True,
                 rotation_range=(-10, 10),
                 translation_range=(-0.1, 0.1),
                 scale_range=(0.9, 1.1),
                 prob=0.5):
        """
        Args:
            enable_rotation: 是否启用旋转
            enable_translation: 是否启用平移
            enable_scaling: 是否启用缩放
            rotation_range: 旋转角度范围（度）
            translation_range: 平移范围（相对于图像尺寸的比例）
            scale_range: 缩放范围
            prob: 应用变换的概率
        """
        self.enable_rotation = enable_rotation
        self.enable_translation = enable_translation
        self.enable_scaling = enable_scaling
        self.rotation_range = rotation_range
        self.translation_range = translation_range
        self.scale_range = scale_range
        self.prob = prob
    
    def __call__(self, image):
        """应用几何变换"""
        if random.random() > self.prob:
            return image
            
        # 转换为numpy数组进行处理
        if isinstance(image, PIL.Image.Image):
            img_array = np.array(image)
            is_pil = True
        else:
            img_array = image
            is_pil = False
            
        h, w = img_array.shape[:2]
        center = (w // 2, h // 2)
        
        # 构建变换矩阵
        M = np.eye(2, 3, dtype=np.float32)
        
        # 旋转
        if self.enable_rotation:
            angle = random.uniform(*self.rotation_range)
            cos_a, sin_a = np.cos(np.radians(angle)), np.sin(np.radians(angle))
            M[0, 0] = cos_a
            M[0, 1] = -sin_a
            M[1, 0] = sin_a
            M[1, 1] = cos_a
        
        # 缩放
        if self.enable_scaling:
            scale = random.uniform(*self.scale_range)
            M[0, 0] *= scale
            M[0, 1] *= scale
            M[1, 0] *= scale
            M[1, 1] *= scale
        
        # 平移
        if self.enable_translation:
            tx = random.uniform(*self.translation_range) * w
            ty = random.uniform(*self.translation_range) * h
            M[0, 2] = tx
            M[1, 2] = ty
        
        # 调整变换中心
        M[0, 2] += center[0] - M[0, 0] * center[0] - M[0, 1] * center[1]
        M[1, 2] += center[1] - M[1, 0] * center[0] - M[1, 1] * center[1]
        
        # 应用变换
        transformed = cv2.warpAffine(img_array, M, (w, h), 
                                   borderMode=cv2.BORDER_REFLECT_101)
        
        # 转换回原始格式
        if is_pil:
            return PIL.Image.fromarray(transformed)
        else:
            return transformed


class CameraShakeSimulation:
    """相机抖动模拟"""
    
    def __init__(self, 
                 max_displacement=3,
                 blur_kernel_size=5,
                 prob=0.3):
        """
        Args:
            max_displacement: 最大位移像素
            blur_kernel_size: 模糊核大小
            prob: 应用抖动的概率
        """
        self.max_displacement = max_displacement
        self.blur_kernel_size = blur_kernel_size
        self.prob = prob
    
    def __call__(self, image):
        """模拟相机抖动"""
        if random.random() > self.prob:
            return image
            
        # 转换为numpy数组
        if isinstance(image, PIL.Image.Image):
            img_array = np.array(image)
            is_pil = True
        else:
            img_array = image
            is_pil = False
        
        # 随机位移
        dx = random.randint(-self.max_displacement, self.max_displacement)
        dy = random.randint(-self.max_displacement, self.max_displacement)
        
        h, w = img_array.shape[:2]
        M = np.float32([[1, 0, dx], [0, 1, dy]])
        shifted = cv2.warpAffine(img_array, M, (w, h), 
                               borderMode=cv2.BORDER_REFLECT_101)
        
        # 轻微模糊模拟抖动
        if self.blur_kernel_size > 1:
            kernel = np.ones((self.blur_kernel_size, self.blur_kernel_size), 
                           np.float32) / (self.blur_kernel_size ** 2)
            shifted = cv2.filter2D(shifted, -1, kernel)
        
        # 转换回原始格式
        if is_pil:
            return PIL.Image.fromarray(shifted.astype(np.uint8))
        else:
            return shifted


class EnhancedMVTecDataset(MVTecDataset):
    """
    增强版MVTec数据集，支持几何变换预处理
    """
    
    def __init__(self,
                 source,
                 classname,
                 resize=256,
                 imagesize=224,
                 split=DatasetSplit.TRAIN,
                 train_val_split=1.0,
                 # 新增参数
                 enable_geometric_aug=False,
                 enable_camera_shake=False,
                 geometric_aug_params=None,
                 camera_shake_params=None,
                 **kwargs):
        """
        Args:
            enable_geometric_aug: 是否启用几何变换增强
            enable_camera_shake: 是否启用相机抖动模拟
            geometric_aug_params: 几何变换参数字典
            camera_shake_params: 相机抖动参数字典
        """
        super().__init__(source, classname, resize, imagesize, split, train_val_split, **kwargs)
        
        self.enable_geometric_aug = enable_geometric_aug
        self.enable_camera_shake = enable_camera_shake
        
        # 初始化几何变换
        if self.enable_geometric_aug:
            if geometric_aug_params is None:
                geometric_aug_params = {}
            self.geometric_aug = GeometricAugmentation(**geometric_aug_params)
        
        # 初始化相机抖动
        if self.enable_camera_shake:
            if camera_shake_params is None:
                camera_shake_params = {}
            self.camera_shake = CameraShakeSimulation(**camera_shake_params)
        
        # 重新构建变换流程
        self._build_transforms()
    
    def _build_transforms(self):
        """构建变换流程"""
        # 图像变换流程
        transform_list = []
        
        # 1. 几何变换（在resize之前应用）
        if self.enable_geometric_aug and self.split == DatasetSplit.TRAIN:
            transform_list.append(self.geometric_aug)
        
        # 2. 相机抖动（在resize之前应用）
        if self.enable_camera_shake:
            transform_list.append(self.camera_shake)
        
        # 3. 标准变换
        transform_list.extend([
            transforms.Resize(self.resize if hasattr(self, 'resize') else 256),
            transforms.CenterCrop(self.imagesize if hasattr(self, 'imagesize') else 224),
            transforms.ToTensor(),
            transforms.Normalize(mean=IMAGENET_MEAN, std=IMAGENET_STD),
        ])
        
        self.transform_img = transforms.Compose(transform_list)
        
        # Mask变换保持不变
        self.transform_mask = transforms.Compose([
            transforms.Resize(self.resize if hasattr(self, 'resize') else 256),
            transforms.CenterCrop(self.imagesize if hasattr(self, 'imagesize') else 224),
            transforms.ToTensor(),
        ])
    
    def __getitem__(self, idx):
        """获取数据项"""
        classname, anomaly, image_path, mask_path = self.data_to_iterate[idx]
        
        # 加载图像
        image = PIL.Image.open(image_path).convert("RGB")
        
        # 应用变换
        image = self.transform_img(image)
        
        # 处理mask
        if self.split == DatasetSplit.TEST and mask_path is not None:
            mask = PIL.Image.open(mask_path)
            mask = self.transform_mask(mask)
        else:
            mask = torch.zeros([1, *image.size()[1:]])
        
        return {
            "image": image,
            "mask": mask,
            "classname": classname,
            "anomaly": anomaly,
            "is_anomaly": int(anomaly != self.normal_class),
            "image_name": "/".join(image_path.split("/")[-4:]),
            "image_path": image_path,
        }


class TestTimeAugmentation:
    """测试时增强（TTA）"""
    
    def __init__(self, 
                 num_augmentations=5,
                 rotation_angles=[-5, -2.5, 0, 2.5, 5],
                 enable_flip=True):
        """
        Args:
            num_augmentations: 增强数量
            rotation_angles: 旋转角度列表
            enable_flip: 是否启用翻转
        """
        self.num_augmentations = num_augmentations
        self.rotation_angles = rotation_angles
        self.enable_flip = enable_flip
    
    def augment_batch(self, images):
        """对批次图像进行TTA"""
        augmented_batches = []
        
        for angle in self.rotation_angles[:self.num_augmentations]:
            # 旋转变换
            if angle != 0:
                rotated = self._rotate_batch(images, angle)
                augmented_batches.append(rotated)
            else:
                augmented_batches.append(images)
        
        # 翻转变换
        if self.enable_flip and len(augmented_batches) < self.num_augmentations:
            flipped = torch.flip(images, dims=[-1])  # 水平翻转
            augmented_batches.append(flipped)
        
        return augmented_batches
    
    def _rotate_batch(self, images, angle):
        """旋转批次图像"""
        rotated_images = []
        for img in images:
            # 转换为PIL图像进行旋转
            img_pil = transforms.ToPILImage()(img)
            rotated_pil = img_pil.rotate(angle, fillcolor=(128, 128, 128))
            rotated_tensor = transforms.ToTensor()(rotated_pil)
            rotated_images.append(rotated_tensor)
        
        return torch.stack(rotated_images)
    
    def aggregate_predictions(self, predictions_list):
        """聚合多个预测结果"""
        # 简单平均
        aggregated_scores = np.mean([pred[0] for pred in predictions_list], axis=0)
        aggregated_masks = np.mean([pred[1] for pred in predictions_list], axis=0)
        
        return aggregated_scores, aggregated_masks
