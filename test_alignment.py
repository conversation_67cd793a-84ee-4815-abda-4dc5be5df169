"""
测试图像对齐模块的功能
"""

import torch
import numpy as np
import cv2
import matplotlib.pyplot as plt
from PIL import Image
import os
import sys

# 添加src路径
sys.path.append('src')
from image_alignment import ImageAlignmentModule


def create_test_images():
    """创建测试图像"""
    # 创建一个简单的测试图像
    base_image = np.zeros((224, 224, 3), dtype=np.float32)
    
    # 添加一些几何图形作为特征
    cv2.rectangle(base_image, (50, 50), (100, 100), (1.0, 0.0, 0.0), -1)  # 红色矩形
    cv2.circle(base_image, (150, 150), 30, (0.0, 1.0, 0.0), -1)  # 绿色圆形
    cv2.line(base_image, (20, 200), (200, 200), (0.0, 0.0, 1.0), 5)  # 蓝色线条
    
    # 转换为tensor格式 [1, 3, 224, 224]
    base_tensor = torch.from_numpy(base_image).permute(2, 0, 1).unsqueeze(0)
    
    # 创建变换后的图像
    transformed_images = []
    
    # 1. 平移变换
    M_translate = np.float32([[1, 0, 10], [0, 1, 15]])  # 向右10像素，向下15像素
    translated = cv2.warpAffine((base_image * 255).astype(np.uint8), M_translate, (224, 224))
    translated_tensor = torch.from_numpy(translated.astype(np.float32) / 255.0).permute(2, 0, 1).unsqueeze(0)
    transformed_images.append(("Translated", translated_tensor))
    
    # 2. 旋转变换
    center = (112, 112)
    M_rotate = cv2.getRotationMatrix2D(center, 5, 1.0)  # 旋转5度
    rotated = cv2.warpAffine((base_image * 255).astype(np.uint8), M_rotate, (224, 224))
    rotated_tensor = torch.from_numpy(rotated.astype(np.float32) / 255.0).permute(2, 0, 1).unsqueeze(0)
    transformed_images.append(("Rotated", rotated_tensor))
    
    # 3. 缩放变换
    M_scale = cv2.getRotationMatrix2D(center, 0, 1.1)  # 放大1.1倍
    scaled = cv2.warpAffine((base_image * 255).astype(np.uint8), M_scale, (224, 224))
    scaled_tensor = torch.from_numpy(scaled.astype(np.float32) / 255.0).permute(2, 0, 1).unsqueeze(0)
    transformed_images.append(("Scaled", scaled_tensor))
    
    return base_tensor, transformed_images


def test_alignment_methods():
    """测试不同的对齐方法"""
    print("创建测试图像...")
    reference_image, transformed_images = create_test_images()
    
    # 测试不同的对齐方法
    alignment_methods = ["feature_based", "phase_correlation", "template_matching"]
    
    results = {}
    
    for method in alignment_methods:
        print(f"\n测试对齐方法: {method}")
        
        # 初始化对齐模块
        aligner = ImageAlignmentModule(
            alignment_method=method,
            reference_mode="template",
            enable_alignment=True
        )
        
        # 设置参考模板
        aligner.set_reference_template(reference_image)
        
        method_results = []
        
        for transform_name, transformed_image in transformed_images:
            print(f"  处理 {transform_name} 图像...")
            
            try:
                # 应用对齐
                aligned_image = aligner.align_images(transformed_image)
                
                # 计算对齐前后的差异
                original_diff = torch.mean(torch.abs(reference_image - transformed_image)).item()
                aligned_diff = torch.mean(torch.abs(reference_image - aligned_image)).item()
                
                improvement = (original_diff - aligned_diff) / original_diff * 100
                
                method_results.append({
                    'transform': transform_name,
                    'original_diff': original_diff,
                    'aligned_diff': aligned_diff,
                    'improvement': improvement,
                    'aligned_image': aligned_image
                })
                
                print(f"    原始差异: {original_diff:.4f}")
                print(f"    对齐后差异: {aligned_diff:.4f}")
                print(f"    改善程度: {improvement:.2f}%")
                
            except Exception as e:
                print(f"    对齐失败: {e}")
                method_results.append({
                    'transform': transform_name,
                    'error': str(e)
                })
        
        results[method] = method_results
    
    return reference_image, transformed_images, results


def visualize_results(reference_image, transformed_images, results):
    """可视化对齐结果"""
    print("\n生成可视化结果...")
    
    # 创建结果目录
    os.makedirs("alignment_test_results", exist_ok=True)
    
    for method, method_results in results.items():
        fig, axes = plt.subplots(2, len(transformed_images) + 1, figsize=(15, 8))
        fig.suptitle(f'Image Alignment Results - {method.upper()}', fontsize=16)
        
        # 显示参考图像
        ref_img = reference_image[0].permute(1, 2, 0).numpy()
        axes[0, 0].imshow(ref_img)
        axes[0, 0].set_title('Reference Image')
        axes[0, 0].axis('off')
        axes[1, 0].axis('off')
        
        for i, (transform_name, transformed_image) in enumerate(transformed_images):
            col = i + 1
            
            # 显示变换后的图像
            trans_img = transformed_image[0].permute(1, 2, 0).numpy()
            axes[0, col].imshow(trans_img)
            axes[0, col].set_title(f'{transform_name}\n(Original)')
            axes[0, col].axis('off')
            
            # 显示对齐后的图像
            result = method_results[i]
            if 'aligned_image' in result:
                aligned_img = result['aligned_image'][0].permute(1, 2, 0).numpy()
                axes[1, col].imshow(aligned_img)
                axes[1, col].set_title(f'{transform_name}\n(Aligned, {result["improvement"]:.1f}% better)')
            else:
                axes[1, col].text(0.5, 0.5, f'Failed:\n{result.get("error", "Unknown error")}', 
                                ha='center', va='center', transform=axes[1, col].transAxes)
                axes[1, col].set_title(f'{transform_name}\n(Failed)')
            axes[1, col].axis('off')
        
        plt.tight_layout()
        plt.savefig(f'alignment_test_results/alignment_results_{method}.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print(f"保存结果图像: alignment_test_results/alignment_results_{method}.png")


def generate_report(results):
    """生成测试报告"""
    print("\n=== 图像对齐模块测试报告 ===")
    
    for method, method_results in results.items():
        print(f"\n{method.upper()} 方法:")
        print("-" * 40)
        
        total_improvement = 0
        success_count = 0
        
        for result in method_results:
            if 'improvement' in result:
                print(f"  {result['transform']:10s}: {result['improvement']:6.2f}% 改善")
                total_improvement += result['improvement']
                success_count += 1
            else:
                print(f"  {result['transform']:10s}: 失败 - {result.get('error', 'Unknown error')}")
        
        if success_count > 0:
            avg_improvement = total_improvement / success_count
            print(f"  平均改善程度: {avg_improvement:.2f}%")
            print(f"  成功率: {success_count}/{len(method_results)} ({success_count/len(method_results)*100:.1f}%)")
        else:
            print("  所有测试都失败了")


def main():
    """主测试函数"""
    print("开始测试图像对齐模块...")
    
    try:
        # 运行对齐测试
        reference_image, transformed_images, results = test_alignment_methods()
        
        # 生成可视化结果
        visualize_results(reference_image, transformed_images, results)
        
        # 生成报告
        generate_report(results)
        
        print("\n测试完成！请查看 alignment_test_results/ 目录中的结果图像。")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
