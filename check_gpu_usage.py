#!/usr/bin/env python3
"""
检查GPU使用情况的脚本
"""

import torch
import sys
import os
import argparse

# 添加src目录到路径
sys.path.insert(0, 'src')
from src import utils


def check_torch_gpu():
    """检查PyTorch GPU配置"""
    print("=" * 60)
    print("PyTorch GPU 配置检查")
    print("=" * 60)
    
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA是否可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"GPU数量: {torch.cuda.device_count()}")
        
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")
    else:
        print("❌ CUDA不可用，将使用CPU")
    
    print()


def test_set_torch_device():
    """测试set_torch_device函数"""
    print("=" * 60)
    print("测试 set_torch_device 函数")
    print("=" * 60)
    
    # 测试不同的GPU ID配置
    test_cases = [
        [],          # 空列表，应该使用CPU
        [0],         # GPU 0
        [1],         # GPU 1（如果存在）
        [0, 1],      # 多GPU（只会使用第一个）
    ]
    
    for gpu_ids in test_cases:
        try:
            device = utils.set_torch_device(gpu_ids)
            print(f"GPU IDs: {gpu_ids} -> Device: {device}")
            
            # 测试设备是否真的可用
            if device.type == 'cuda':
                if torch.cuda.is_available() and device.index < torch.cuda.device_count():
                    # 尝试在设备上创建tensor
                    test_tensor = torch.randn(10, 10).to(device)
                    print(f"  ✅ 成功在 {device} 上创建tensor")
                    print(f"  📊 GPU内存使用: {torch.cuda.memory_allocated(device.index) / 1024**2:.1f} MB")
                else:
                    print(f"  ❌ GPU {device.index} 不可用")
            else:
                test_tensor = torch.randn(10, 10).to(device)
                print(f"  ✅ 成功在CPU上创建tensor")
                
        except Exception as e:
            print(f"GPU IDs: {gpu_ids} -> ❌ 错误: {e}")
    
    print()


def test_main_args():
    """测试main.py的参数解析"""
    print("=" * 60)
    print("测试 main.py 参数解析")
    print("=" * 60)
    
    # 模拟main.py的参数解析
    test_commands = [
        ["--gpu", "0"],
        ["--gpu", "1"],
        ["--gpu", "0", "--gpu", "1"],  # 多GPU
        [],  # 无GPU参数
    ]
    
    for cmd_args in test_commands:
        try:
            # 创建参数解析器（简化版）
            parser = argparse.ArgumentParser()
            parser.add_argument('--gpu', type=int, default=[], action='append')
            
            args = parser.parse_args(cmd_args)
            device = utils.set_torch_device(args.gpu)
            
            print(f"命令行参数: {cmd_args}")
            print(f"  解析的GPU IDs: {args.gpu}")
            print(f"  设置的设备: {device}")
            
            if device.type == 'cuda':
                print(f"  🎯 将使用GPU {device.index}")
            else:
                print(f"  🖥️  将使用CPU")
                
        except Exception as e:
            print(f"命令行参数: {cmd_args} -> ❌ 错误: {e}")
    
    print()


def test_gpu_memory_usage():
    """测试GPU内存使用"""
    print("=" * 60)
    print("GPU内存使用测试")
    print("=" * 60)
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，跳过GPU内存测试")
        return
    
    device = torch.device("cuda:0")
    
    print(f"测试设备: {device}")
    print(f"GPU名称: {torch.cuda.get_device_name(device)}")
    
    # 获取初始内存状态
    torch.cuda.empty_cache()
    initial_memory = torch.cuda.memory_allocated(device)
    total_memory = torch.cuda.get_device_properties(device).total_memory
    
    print(f"总内存: {total_memory / 1024**3:.1f} GB")
    print(f"初始已用内存: {initial_memory / 1024**2:.1f} MB")
    
    # 创建一些tensor测试内存使用
    print("\n创建测试tensor...")
    tensors = []
    
    for i in range(5):
        size = 1000 * (i + 1)
        tensor = torch.randn(size, size).to(device)
        tensors.append(tensor)
        
        current_memory = torch.cuda.memory_allocated(device)
        print(f"  Tensor {i+1} ({size}x{size}): {current_memory / 1024**2:.1f} MB")
    
    # 清理内存
    del tensors
    torch.cuda.empty_cache()
    final_memory = torch.cuda.memory_allocated(device)
    print(f"\n清理后内存: {final_memory / 1024**2:.1f} MB")
    
    print()


def simulate_softpatch_gpu_usage():
    """模拟SoftPatch的GPU使用"""
    print("=" * 60)
    print("模拟 SoftPatch GPU 使用")
    print("=" * 60)
    
    # 测试--gpu 0参数
    gpu_ids = [0]
    device = utils.set_torch_device(gpu_ids)
    
    print(f"模拟命令: python main.py --gpu 0")
    print(f"设置的设备: {device}")
    
    if device.type == 'cuda':
        if torch.cuda.is_available() and device.index < torch.cuda.device_count():
            print(f"✅ GPU {device.index} 可用")
            
            # 模拟一些典型的SoftPatch操作
            print("\n模拟SoftPatch操作:")
            
            # 1. 模拟图像batch
            batch_size = 8
            image_batch = torch.randn(batch_size, 3, 224, 224).to(device)
            print(f"  📸 图像batch: {image_batch.shape} on {image_batch.device}")
            
            # 2. 模拟特征提取
            features = torch.randn(batch_size, 512, 28, 28).to(device)
            print(f"  🔍 特征提取: {features.shape} on {features.device}")
            
            # 3. 模拟patch处理
            patches = features.view(batch_size, 512, -1).permute(0, 2, 1)
            print(f"  🧩 Patch处理: {patches.shape} on {patches.device}")
            
            # 4. 检查内存使用
            if torch.cuda.is_available():
                memory_used = torch.cuda.memory_allocated(device) / 1024**2
                print(f"  💾 GPU内存使用: {memory_used:.1f} MB")
            
            print("  ✅ 所有操作都在GPU上成功执行")
            
        else:
            print(f"❌ GPU {device.index} 不可用")
    else:
        print("🖥️  将在CPU上运行")
    
    print()


def check_faiss_gpu():
    """检查Faiss GPU支持"""
    print("=" * 60)
    print("Faiss GPU 支持检查")
    print("=" * 60)
    
    try:
        import faiss
        print(f"Faiss版本: {faiss.__version__}")
        
        # 检查GPU支持
        if hasattr(faiss, 'get_num_gpus'):
            num_gpus = faiss.get_num_gpus()
            print(f"Faiss检测到的GPU数量: {num_gpus}")
            
            if num_gpus > 0:
                print("✅ Faiss支持GPU加速")
                print("  可以使用 --faiss_on_gpu 参数")
            else:
                print("❌ Faiss未检测到GPU")
        else:
            print("⚠️  无法检测Faiss GPU支持")
            
    except ImportError:
        print("❌ Faiss未安装")
    except Exception as e:
        print(f"❌ Faiss检查失败: {e}")
    
    print()


def main():
    """主函数"""
    print("🔍 SoftPatch GPU使用情况检查")
    print("=" * 60)
    
    # 运行所有检查
    check_torch_gpu()
    test_set_torch_device()
    test_main_args()
    
    if torch.cuda.is_available():
        test_gpu_memory_usage()
    
    simulate_softpatch_gpu_usage()
    check_faiss_gpu()
    
    print("=" * 60)
    print("检查完成！")
    print("=" * 60)
    
    # 总结建议
    print("\n💡 使用建议:")
    
    if torch.cuda.is_available():
        print("✅ GPU可用，推荐使用以下命令:")
        print("   python main.py --gpu 0 --faiss_on_gpu")
        print("   这将同时在GPU上运行模型和Faiss索引")
    else:
        print("❌ GPU不可用，使用CPU:")
        print("   python main.py")
        print("   (不需要--gpu参数)")
    
    print("\n📊 参数说明:")
    print("   --gpu 0           # 使用GPU 0")
    print("   --gpu 1           # 使用GPU 1")
    print("   --faiss_on_gpu    # Faiss索引也使用GPU")
    print("   --faiss_num_workers 4  # Faiss工作线程数")


if __name__ == "__main__":
    main()
