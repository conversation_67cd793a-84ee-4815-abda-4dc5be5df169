#!/usr/bin/env python3
"""
测试噪声增强功能的脚本
"""

import torch
import numpy as np
from PIL import Image
from torchvision import transforms
import matplotlib.pyplot as plt
import sys
import os

# 添加src目录到路径
sys.path.insert(0, 'src')
from src.datasets import AddSaltPepperNoise, AddGaussianNoise


def create_test_images():
    """创建测试图像"""
    # 创建不同格式的测试图像
    test_images = {}
    
    # 1. RGB PIL图像
    rgb_array = np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8)
    test_images['RGB_PIL'] = Image.fromarray(rgb_array)
    
    # 2. 灰度PIL图像
    gray_array = np.random.randint(0, 255, (224, 224), dtype=np.uint8)
    test_images['Gray_PIL'] = Image.fromarray(gray_array, mode='L')
    
    # 3. RGB Tensor
    test_images['RGB_Tensor'] = transforms.ToTensor()(test_images['RGB_PIL'])
    
    # 4. 灰度Tensor
    test_images['Gray_Tensor'] = transforms.ToTensor()(test_images['Gray_PIL'])
    
    return test_images


def test_salt_pepper_noise():
    """测试椒盐噪声"""
    print("测试椒盐噪声...")
    
    # 创建噪声变换
    salt_pepper = AddSaltPepperNoise(density=0.1, prob=1.0)  # 100%概率应用
    
    test_images = create_test_images()
    
    for name, img in test_images.items():
        try:
            print(f"  测试 {name}...")
            if isinstance(img, torch.Tensor):
                print(f"    输入tensor形状: {img.shape}")
            else:
                print(f"    输入PIL图像模式: {img.mode}, 尺寸: {img.size}")
            
            # 应用噪声
            noisy_img = salt_pepper(img)
            
            if isinstance(noisy_img, Image.Image):
                print(f"    输出PIL图像模式: {noisy_img.mode}, 尺寸: {noisy_img.size}")
            else:
                print(f"    输出类型: {type(noisy_img)}")
            
            print(f"    ✅ {name} 测试成功")
            
        except Exception as e:
            print(f"    ❌ {name} 测试失败: {e}")
    
    print()


def test_gaussian_noise():
    """测试高斯噪声"""
    print("测试高斯噪声...")
    
    # 创建噪声变换
    gaussian_noise = AddGaussianNoise(mean=0, std=0.1, prob=1.0)  # 100%概率应用
    
    test_images = create_test_images()
    
    for name, img in test_images.items():
        try:
            print(f"  测试 {name}...")
            if isinstance(img, torch.Tensor):
                print(f"    输入tensor形状: {img.shape}")
            else:
                print(f"    输入PIL图像模式: {img.mode}, 尺寸: {img.size}")
            
            # 应用噪声
            noisy_img = gaussian_noise(img)
            
            if isinstance(noisy_img, Image.Image):
                print(f"    输出PIL图像模式: {noisy_img.mode}, 尺寸: {noisy_img.size}")
            else:
                print(f"    输出类型: {type(noisy_img)}")
            
            print(f"    ✅ {name} 测试成功")
            
        except Exception as e:
            print(f"    ❌ {name} 测试失败: {e}")
    
    print()


def test_noise_dataset():
    """测试NoiseDataset类"""
    print("测试NoiseDataset类...")
    
    try:
        from src.datasets import NoiseDataset
        
        # 创建模拟数据源
        class MockDataset:
            def __init__(self):
                self.data = []
                for i in range(5):
                    # 创建不同格式的图像
                    if i % 2 == 0:
                        img = Image.fromarray(np.random.randint(0, 255, (224, 224, 3), dtype=np.uint8))
                    else:
                        img = Image.fromarray(np.random.randint(0, 255, (224, 224), dtype=np.uint8), mode='L')
                    
                    self.data.append({
                        'image': img,
                        'mask': torch.zeros(1, 224, 224),
                        'is_anomaly': 1,
                        'classname': 'test'
                    })
            
            def __len__(self):
                return len(self.data)
            
            def __getitem__(self, idx):
                return self.data[idx]
        
        # 创建NoiseDataset
        mock_source = MockDataset()
        noise_dataset = NoiseDataset(
            source=mock_source,
            enable_rotation=True,
            enable_affine=True,
            enable_noise=True,
            rotation_degrees=10
        )
        
        print(f"  NoiseDataset长度: {len(noise_dataset)}")
        
        # 测试获取数据
        for i in range(min(3, len(noise_dataset))):
            try:
                item = noise_dataset[i]
                img = item['image']
                print(f"  样本 {i}: 图像模式={img.mode}, 尺寸={img.size}")
                print(f"    ✅ 样本 {i} 测试成功")
            except Exception as e:
                print(f"    ❌ 样本 {i} 测试失败: {e}")
        
        print("  ✅ NoiseDataset 测试成功")
        
    except Exception as e:
        print(f"  ❌ NoiseDataset 测试失败: {e}")
    
    print()


def visualize_noise_effects():
    """可视化噪声效果"""
    print("生成噪声效果可视化...")
    
    try:
        # 创建原始图像
        original = Image.fromarray(np.random.randint(100, 200, (224, 224, 3), dtype=np.uint8))
        
        # 创建不同的噪声变换
        salt_pepper_light = AddSaltPepperNoise(density=0.05, prob=1.0)
        salt_pepper_heavy = AddSaltPepperNoise(density=0.15, prob=1.0)
        gaussian_light = AddGaussianNoise(mean=0, std=0.05, prob=1.0)
        gaussian_heavy = AddGaussianNoise(mean=0, std=0.15, prob=1.0)
        
        # 应用变换
        images = {
            'Original': original,
            'Salt-Pepper Light': salt_pepper_light(original),
            'Salt-Pepper Heavy': salt_pepper_heavy(original),
            'Gaussian Light': gaussian_light(original),
            'Gaussian Heavy': gaussian_heavy(original)
        }
        
        # 创建可视化
        fig, axes = plt.subplots(1, 5, figsize=(20, 4))
        
        for i, (title, img) in enumerate(images.items()):
            axes[i].imshow(img)
            axes[i].set_title(title)
            axes[i].axis('off')
        
        plt.tight_layout()
        plt.savefig('noise_effects_visualization.png', dpi=150, bbox_inches='tight')
        print("  ✅ 可视化已保存为 'noise_effects_visualization.png'")
        
    except Exception as e:
        print(f"  ❌ 可视化生成失败: {e}")
    
    print()


def test_transform_pipeline():
    """测试完整的变换流水线"""
    print("测试完整变换流水线...")
    
    try:
        # 创建完整的变换流水线
        transform_pipeline = transforms.Compose([
            transforms.Resize(256),
            transforms.CenterCrop(224),
            AddSaltPepperNoise(density=0.05, prob=0.5),
            AddGaussianNoise(mean=0, std=0.05, prob=0.3),
            transforms.RandomRotation(10),
            transforms.RandomAffine(10, translate=(0.1, 0.1), scale=(0.9, 1.1)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
        
        # 测试不同输入
        test_images = create_test_images()
        
        for name, img in test_images.items():
            if isinstance(img, torch.Tensor):
                # 跳过tensor输入，因为pipeline期望PIL图像
                continue
                
            try:
                print(f"  测试流水线 - {name}...")
                result = transform_pipeline(img)
                print(f"    输出tensor形状: {result.shape}")
                print(f"    输出数据范围: [{result.min():.3f}, {result.max():.3f}]")
                print(f"    ✅ 流水线测试成功")
            except Exception as e:
                print(f"    ❌ 流水线测试失败: {e}")
        
    except Exception as e:
        print(f"  ❌ 流水线测试失败: {e}")
    
    print()


def main():
    """主测试函数"""
    print("=" * 60)
    print("噪声增强功能测试")
    print("=" * 60)
    print()
    
    # 运行所有测试
    test_salt_pepper_noise()
    test_gaussian_noise()
    test_noise_dataset()
    test_transform_pipeline()
    visualize_noise_effects()
    
    print("=" * 60)
    print("测试完成！")
    print("=" * 60)
    
    print("\n使用建议:")
    print("1. 如果所有测试都通过，说明噪声增强功能已修复")
    print("2. 可以安全使用 --noise_augmentation 参数")
    print("3. 建议的参数组合:")
    print("   --noise_augmentation --noise 0.1 --aug_rotation_degrees 10")
    print("4. 查看生成的 'noise_effects_visualization.png' 了解噪声效果")


if __name__ == "__main__":
    main()
