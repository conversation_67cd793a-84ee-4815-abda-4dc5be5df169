#!/usr/bin/env python3
"""
使用图像对齐功能运行SoftPatch的示例脚本
"""

import subprocess
import sys
import os


def run_softpatch_with_alignment():
    """运行带有图像对齐功能的SoftPatch"""
    
    # 基本参数
    base_cmd = [
        sys.executable, "main.py",
        "--dataset", "mvtec",
        "--data_path", "path/to/your/mvtec/data",  # 请修改为您的数据路径
        "--subdatasets", "bottle",  # 可以修改为其他类别
        "--backbone_names", "wideresnet50",
        "--layers_to_extract_from", "layer2", "layer3",
        "--batch_size", "8",
        "--imagesize", "224",
        "--resize", "256"
    ]
    
    # 图像对齐参数
    alignment_cmd = [
        "--enable_image_alignment",  # 启用图像对齐
        "--alignment_method", "feature_based",  # 使用基于特征点的对齐
        "--reference_mode", "template"  # 使用模板参考模式
    ]
    
    # 组合完整命令
    full_cmd = base_cmd + alignment_cmd
    
    print("运行命令:")
    print(" ".join(full_cmd))
    print("\n" + "="*50)
    
    try:
        # 运行命令
        result = subprocess.run(full_cmd, check=True, capture_output=False)
        print("\n" + "="*50)
        print("运行成功完成！")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"\n运行失败，错误码: {e.returncode}")
        return False
    except FileNotFoundError:
        print("\n错误: 找不到main.py文件，请确保在正确的目录中运行此脚本")
        return False


def run_comparison_test():
    """运行对比测试：有对齐 vs 无对齐"""
    
    print("开始对比测试...")
    print("1. 运行无图像对齐的版本")
    
    # 无对齐版本
    base_cmd = [
        sys.executable, "main.py",
        "--dataset", "mvtec",
        "--data_path", "path/to/your/mvtec/data",  # 请修改为您的数据路径
        "--subdatasets", "bottle",
        "--backbone_names", "wideresnet50",
        "--layers_to_extract_from", "layer2", "layer3",
        "--batch_size", "8",
        "--results_path", "results_no_alignment"
    ]
    
    try:
        print("运行无对齐版本...")
        subprocess.run(base_cmd, check=True)
        print("无对齐版本完成")
    except subprocess.CalledProcessError:
        print("无对齐版本运行失败")
        return False
    
    print("\n2. 运行有图像对齐的版本")
    
    # 有对齐版本
    alignment_cmd = base_cmd.copy()
    alignment_cmd.extend([
        "--enable_image_alignment",
        "--alignment_method", "feature_based",
        "--reference_mode", "template"
    ])
    alignment_cmd[alignment_cmd.index("results_no_alignment")] = "results_with_alignment"
    
    try:
        print("运行有对齐版本...")
        subprocess.run(alignment_cmd, check=True)
        print("有对齐版本完成")
    except subprocess.CalledProcessError:
        print("有对齐版本运行失败")
        return False
    
    print("\n对比测试完成！")
    print("请查看以下目录中的结果:")
    print("- results_no_alignment/")
    print("- results_with_alignment/")
    
    return True


def show_usage_examples():
    """显示使用示例"""
    
    print("图像对齐模块使用示例:")
    print("="*50)
    
    examples = [
        {
            "name": "基本使用（推荐）",
            "cmd": [
                "python main.py",
                "--dataset mvtec",
                "--data_path /path/to/data",
                "--subdatasets bottle",
                "--enable_image_alignment",
                "--alignment_method feature_based",
                "--reference_mode template"
            ]
        },
        {
            "name": "快速对齐（性能优先）",
            "cmd": [
                "python main.py",
                "--dataset mvtec", 
                "--data_path /path/to/data",
                "--subdatasets bottle",
                "--enable_image_alignment",
                "--alignment_method phase_correlation",
                "--reference_mode template"
            ]
        },
        {
            "name": "自适应对齐（质量优先）",
            "cmd": [
                "python main.py",
                "--dataset mvtec",
                "--data_path /path/to/data", 
                "--subdatasets bottle",
                "--enable_image_alignment",
                "--alignment_method feature_based",
                "--reference_mode adaptive"
            ]
        },
        {
            "name": "禁用对齐（原始模型）",
            "cmd": [
                "python main.py",
                "--dataset mvtec",
                "--data_path /path/to/data",
                "--subdatasets bottle"
                # 不添加 --enable_image_alignment
            ]
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['name']}:")
        print("   " + " \\\n   ".join(example['cmd']))
    
    print("\n" + "="*50)
    print("参数说明:")
    print("--enable_image_alignment     启用图像对齐功能")
    print("--alignment_method          对齐方法:")
    print("  - feature_based           基于特征点（推荐，适用于纹理丰富的图像）")
    print("  - phase_correlation       相位相关（快速，适用于轻微位移）")
    print("  - template_matching       模板匹配（简单，适用于基本场景）")
    print("--reference_mode            参考模式:")
    print("  - template                使用第一张图像作为参考")
    print("  - centroid                使用所有图像的平均值")
    print("  - adaptive                自动选择最佳参考图像")


def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "run":
            run_softpatch_with_alignment()
        elif sys.argv[1] == "compare":
            run_comparison_test()
        elif sys.argv[1] == "examples":
            show_usage_examples()
        else:
            print("未知参数，请使用: run, compare, 或 examples")
    else:
        print("图像对齐模块使用脚本")
        print("="*30)
        print("用法:")
        print("  python run_with_alignment.py run       # 运行带对齐的SoftPatch")
        print("  python run_with_alignment.py compare   # 运行对比测试")
        print("  python run_with_alignment.py examples  # 显示使用示例")
        print("\n注意: 请先修改脚本中的数据路径!")


if __name__ == "__main__":
    main()
