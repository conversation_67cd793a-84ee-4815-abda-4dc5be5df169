import random
import PIL
import torch
from torchvision import transforms
import numpy as np
from PIL import Image


class AddSaltPepperNoise(object):

    def __init__(self, density=0.0, prob=0.5):
        self.density = density
        self.prob = prob

    def __call__(self, img):
        if random.uniform(0, 1) < self.prob:
            # 确保输入是PIL图像
            if not isinstance(img, Image.Image):
                if isinstance(img, torch.Tensor):
                    # 如果是tensor，转换为PIL图像
                    img = transforms.ToPILImage()(img)
                else:
                    # 如果是numpy数组，转换为PIL图像
                    try:
                        # 处理不同的numpy数组格式
                        if isinstance(img, np.ndarray):
                            # 确保数组是正确的格式
                            if img.dtype != np.uint8:
                                # 如果不是uint8，先转换数据范围
                                if img.max() <= 1.0:
                                    img = (img * 255).astype(np.uint8)
                                else:
                                    img = img.astype(np.uint8)

                            # 处理形状
                            if len(img.shape) == 2:
                                # 灰度图
                                img = Image.fromarray(img, mode='L')
                            elif len(img.shape) == 3:
                                if img.shape[2] == 1:
                                    # 单通道，转为灰度
                                    img = Image.fromarray(img.squeeze(), mode='L')
                                elif img.shape[2] == 3:
                                    # RGB
                                    img = Image.fromarray(img, mode='RGB')
                                elif img.shape[2] == 4:
                                    # RGBA
                                    img = Image.fromarray(img, mode='RGBA')
                                else:
                                    # 不支持的通道数，返回原始输入
                                    return img
                            else:
                                # 不支持的维度，返回原始输入
                                return img
                        else:
                            img = Image.fromarray(img)
                    except Exception as e:
                        print(f"Warning: Failed to convert input to PIL Image: {e}")
                        return img

            # 转换为numpy数组进行处理
            img_array = np.array(img)

            # 处理不同的图像格式
            if len(img_array.shape) == 2:
                # 灰度图像，转换为RGB
                img_array = np.stack([img_array] * 3, axis=-1)
            elif len(img_array.shape) == 3:
                if img_array.shape[2] == 1:
                    # 单通道图像，转换为RGB
                    img_array = np.repeat(img_array, 3, axis=2)
                elif img_array.shape[2] == 4:
                    # RGBA图像，只取RGB通道
                    img_array = img_array[:, :, :3]
                elif img_array.shape[2] != 3:
                    # 其他通道数，直接返回原图像
                    return img
            else:
                # 不支持的格式，返回原图像
                return img

            height, width, channel = img_array.shape
            density = self.density
            s_d = 1 - density

            # 生成噪声mask
            mask = np.random.choice((0, 1, 2), size=(height, width, 1),
                                  p=[density / 2.0, density / 2.0, s_d])
            mask = np.repeat(mask, channel, axis=2)

            # 应用椒盐噪声
            img_array[mask == 0] = 0    # 胡椒噪声（黑点）
            img_array[mask == 1] = 255  # 盐噪声（白点）

            # 转换回PIL图像
            try:
                img = Image.fromarray(img_array.astype('uint8')).convert('RGB')
            except Exception as e:
                # 如果转换失败，返回原图像
                print(f"Warning: Failed to apply salt-pepper noise: {e}")
                return img

            return img
        else:
            return img


class AddGaussianNoise(object):
    def __init__(self, mean=0., std=1., prob=0.5):
        self.std = std
        self.mean = mean
        self.prob = prob

    def __call__(self, img):
        if random.uniform(0, 1) < self.prob:
            # 如果输入是PIL图像，先转换为tensor
            if isinstance(img, Image.Image):
                img = transforms.ToTensor()(img)

            # 确保是tensor格式
            if not isinstance(img, torch.Tensor):
                img = torch.from_numpy(np.array(img)).float()
                if len(img.shape) == 3:
                    img = img.permute(2, 0, 1)  # HWC -> CHW
                img = img / 255.0  # 归一化到[0,1]

            # 添加高斯噪声
            noise = torch.randn(img.size()) * self.std + self.mean
            noisy_img = img + noise

            # 限制到有效范围
            noisy_img = torch.clamp(noisy_img, 0, 1)

            # 转换回PIL图像
            img = transforms.ToPILImage()(noisy_img)
            return img
        else:
            return img

    def __repr__(self):
        return self.__class__.__name__ + '(mean={0}, std={1}, prob={2})'.format(self.mean, self.std, self.prob)


class NoiseDataset(torch.utils.data.Dataset):
    def __init__(
            self,
            source,
            enable_rotation=True,
            enable_affine=True,
            enable_noise=True,
            rotation_degrees=10,
            translation=(0.1, 0.1),
            scale=(0.9, 1.1),
            shear=10
    ):
        self.source = source

        # 构建变换列表
        transform_list = []

        if enable_rotation:
            transform_list.append(transforms.RandomRotation(rotation_degrees))

        if enable_affine:
            transform_list.append(
                transforms.RandomAffine(
                    degrees=rotation_degrees if enable_rotation else 0,
                    translate=translation,
                    scale=scale,
                    shear=shear
                )
            )

        if enable_noise:
            transform_list.extend([
                AddSaltPepperNoise(0.05, 0.5),  # 降低噪声概率
                # AddGaussianNoise(std=0.05),
            ])

        # 如果没有任何变换，至少保留一个恒等变换
        if not transform_list:
            transform_list.append(transforms.Lambda(lambda x: x))

        self.transform_noise = transforms.Compose(transform_list)


    def __len__(self):
        return len(self.source)

    def __getitem__(self, idx):
        item = self.source[idx]

        item["image"] = self.transform_noise(item["image"])
        return item
